# Tasks: Smart AI-Integrated Backlog Management System

**Input**: Design documents from `/home/<USER>/dev/backlogger2/specs/001-backlogger-is-a/`
**Prerequisites**: plan.md (required), research.md, data-model.md, contracts/

## Execution Flow (main)
```
1. Load plan.md from feature directory
   → SUCCESS: Implementation plan found with FastAPI + SQLAlchemy + Dagster
   → Extracted: Python 3.11+, uv package manager, web app structure
2. Load optional design documents:
   → data-model.md: 5 entities (Project, Epic, Feature, BacklogItem, Task)
   → contracts/: API contracts for all CRUD operations + AI workflow
   → research.md: Technology decisions and architecture patterns
   → quickstart.md: 5 core user scenarios for integration testing
3. Generate tasks by category:
   → Setup: uv init, FastAPI/SQLAlchemy/Dagster dependencies
   → Tests: 15 contract tests, 5 integration tests from scenarios
   → Core: 5 SQLAlchemy models, business logic, CLI commands
   → AI Integration: Dagster workflows, sequential task processing
   → Dashboard: Jinja2 templates, WebSocket real-time updates
   → Polish: unit tests, performance validation, documentation
4. Apply task rules:
   → Model tasks [P] (different entity files)
   → Contract tests [P] (different endpoint files)
   → Integration tests [P] (different scenario files)
   → Same-file implementations sequential
5. Number tasks sequentially (T001-T040)
6. TDD ordering: All tests before implementations
7. Dependencies: Models → Services → API → AI → Dashboard
8. SUCCESS: 40 tasks ready for execution
```

## Format: `[ID] [P?] Description`
- **[P]**: Can run in parallel (different files, no dependencies)
- Include exact file paths in descriptions

## Path Conventions
- **Web app structure**: `src/backlogger/` with submodules
- **Tests**: `tests/contract/`, `tests/integration/`, `tests/unit/`
- **Project root**: `pyproject.toml`, configuration files

## Phase 3.1: Setup
- [ ] T001 Create project structure with uv init and configure pyproject.toml for backlogger package
- [ ] T002 Add FastAPI, SQLAlchemy, Dagster, Jinja2, pytest dependencies via uv add
- [ ] T003 [P] Configure development tools: black, flake8, mypy in pyproject.toml
- [ ] T004 Create src/backlogger/__init__.py and basic package structure
- [ ] T005 [P] Setup database configuration and SQLite connection in src/backlogger/core/database.py

## Phase 3.2: Tests First (TDD) ⚠️ MUST COMPLETE BEFORE 3.3
**CRITICAL: These tests MUST be written and MUST FAIL before ANY implementation**

### Contract Tests (API Endpoints)
- [ ] T006 [P] Contract test GET /api/v1/projects in tests/contract/test_projects_get.py
- [ ] T007 [P] Contract test POST /api/v1/projects in tests/contract/test_projects_post.py
- [ ] T008 [P] Contract test PUT /api/v1/projects/{id} in tests/contract/test_projects_put.py
- [ ] T009 [P] Contract test DELETE /api/v1/projects/{id} in tests/contract/test_projects_delete.py
- [ ] T010 [P] Contract test GET /api/v1/projects/{id}/epics in tests/contract/test_epics_get.py
- [ ] T011 [P] Contract test POST /api/v1/projects/{id}/epics in tests/contract/test_epics_post.py
- [ ] T012 [P] Contract test PUT /api/v1/epics/{id} in tests/contract/test_epics_put.py
- [ ] T013 [P] Contract test POST /api/v1/backlog-items/{id}/ready-for-ai in tests/contract/test_ai_workflow.py
- [ ] T014 [P] Contract test GET /api/v1/backlog-items/{id}/ai-status in tests/contract/test_ai_status.py
- [ ] T015 [P] Contract test WebSocket /ws/dashboard in tests/contract/test_websocket.py

### Integration Tests (User Scenarios)
- [ ] T016 [P] Integration test project hierarchy creation in tests/integration/test_hierarchy_creation.py
- [ ] T017 [P] Integration test AI workflow automation in tests/integration/test_ai_automation.py
- [ ] T018 [P] Integration test real-time dashboard updates in tests/integration/test_realtime_updates.py
- [ ] T019 [P] Integration test API validation scenarios in tests/integration/test_api_validation.py
- [ ] T020 [P] Integration test error handling edge cases in tests/integration/test_error_handling.py

## Phase 3.3: Core Implementation (ONLY after tests are failing)

### Database Models
- [ ] T021 [P] Project model with status enum in src/backlogger/models/project.py
- [ ] T022 [P] Epic model with relationships in src/backlogger/models/epic.py
- [ ] T023 [P] Feature model with validation in src/backlogger/models/feature.py
- [ ] T024 [P] BacklogItem model with AI workflow fields in src/backlogger/models/backlog_item.py
- [ ] T025 [P] Task model with processing order in src/backlogger/models/task.py
- [ ] T026 Database schema creation and migration in src/backlogger/core/schema.py

### Business Logic Libraries
- [ ] T027 [P] Project service with CRUD operations in src/backlogger/core/project_service.py
- [ ] T028 [P] Epic service with status transitions in src/backlogger/core/epic_service.py
- [ ] T029 [P] Feature service with validation rules in src/backlogger/core/feature_service.py
- [ ] T030 BacklogItem service with AI workflow triggers in src/backlogger/core/backlog_service.py
- [ ] T031 Task service with sequential processing in src/backlogger/core/task_service.py

### API Endpoints
- [ ] T032 FastAPI app setup and project endpoints in src/backlogger/api/projects.py
- [ ] T033 Epic and feature endpoints with validation in src/backlogger/api/work_items.py
- [ ] T034 BacklogItem and task endpoints in src/backlogger/api/backlog.py
- [ ] T035 AI workflow trigger endpoints in src/backlogger/api/ai_workflows.py
- [ ] T036 WebSocket handler for real-time updates in src/backlogger/api/websocket.py

## Phase 3.4: AI Integration
- [ ] T037 Dagster asset definitions for AI workflows in src/backlogger/ai_workflows/pipelines.py
- [ ] T038 Sequential task processor with 1-minute delays in src/backlogger/ai_workflows/task_processor.py
- [ ] T039 AI workflow state management and error handling in src/backlogger/ai_workflows/workflow_manager.py

## Phase 3.5: Dashboard UI
- [ ] T040 Jinja2 template base layout in src/backlogger/dashboard/templates/base.html
- [ ] T041 Project list and detail templates in src/backlogger/dashboard/templates/projects/
- [ ] T042 Real-time dashboard with WebSocket integration in src/backlogger/dashboard/templates/dashboard.html
- [ ] T043 Dashboard route handlers in src/backlogger/dashboard/routes.py
- [ ] T044 Static assets and CSS styling in src/backlogger/dashboard/static/

## Phase 3.6: Integration & CLI
- [ ] T045 Main application entry point in src/backlogger/main.py
- [ ] T046 CLI interface with --help and commands in src/backlogger/cli/main.py
- [ ] T047 Database initialization command in src/backlogger/cli/database.py
- [ ] T048 Logging configuration and middleware in src/backlogger/core/logging.py
- [ ] T049 Error handling and validation middleware in src/backlogger/api/middleware.py

## Phase 3.7: Polish
- [ ] T050 [P] Unit tests for model validation in tests/unit/test_model_validation.py
- [ ] T051 [P] Unit tests for business logic in tests/unit/test_services.py
- [ ] T052 [P] Performance tests for API response times in tests/performance/test_api_performance.py
- [ ] T053 [P] Load tests for concurrent users in tests/performance/test_load.py
- [ ] T054 Execute quickstart.md validation scenarios manually
- [ ] T055 [P] Update README.md with installation and usage instructions
- [ ] T056 Code quality review and refactoring cleanup

## Dependencies
- Setup (T001-T005) before all other phases
- All tests (T006-T020) before implementations (T021-T049)
- Models (T021-T026) before services (T027-T031)
- Services before API endpoints (T032-T036)
- Core API before AI integration (T037-T039)
- Backend complete before dashboard (T040-T044)
- Integration (T045-T049) before polish (T050-T056)

## Parallel Execution Examples

### Contract Tests Phase (T006-T015)
```bash
# Launch all contract tests in parallel
Task: "Contract test GET /api/v1/projects in tests/contract/test_projects_get.py"
Task: "Contract test POST /api/v1/projects in tests/contract/test_projects_post.py"
Task: "Contract test PUT /api/v1/projects/{id} in tests/contract/test_projects_put.py"
Task: "Contract test DELETE /api/v1/projects/{id} in tests/contract/test_projects_delete.py"
Task: "Contract test GET /api/v1/projects/{id}/epics in tests/contract/test_epics_get.py"
```

### Integration Tests Phase (T016-T020)
```bash
# Launch all integration tests in parallel
Task: "Integration test project hierarchy creation in tests/integration/test_hierarchy_creation.py"
Task: "Integration test AI workflow automation in tests/integration/test_ai_automation.py"
Task: "Integration test real-time dashboard updates in tests/integration/test_realtime_updates.py"
Task: "Integration test API validation scenarios in tests/integration/test_api_validation.py"
Task: "Integration test error handling edge cases in tests/integration/test_error_handling.py"
```

### Model Creation Phase (T021-T025)
```bash
# Launch all model files in parallel
Task: "Project model with status enum in src/backlogger/models/project.py"
Task: "Epic model with relationships in src/backlogger/models/epic.py"
Task: "Feature model with validation in src/backlogger/models/feature.py"
Task: "BacklogItem model with AI workflow fields in src/backlogger/models/backlog_item.py"
Task: "Task model with processing order in src/backlogger/models/task.py"
```

### Business Logic Phase (T027-T029)
```bash
# Launch independent service files in parallel
Task: "Project service with CRUD operations in src/backlogger/core/project_service.py"
Task: "Epic service with status transitions in src/backlogger/core/epic_service.py"
Task: "Feature service with validation rules in src/backlogger/core/feature_service.py"
# Note: T030-T031 depend on these and run sequentially
```

## Notes
- [P] tasks = different files, no dependencies between them
- Always verify tests fail before implementing (RED-GREEN-REFACTOR)
- Commit after each task completion
- Use uv run for all Python executions
- Follow FastAPI async patterns throughout
- Ensure WebSocket connections are properly managed
- AI workflows must process tasks sequentially, not in parallel

## Task Generation Rules Applied
✅ All contract endpoints have corresponding test tasks  
✅ All entities have model creation tasks  
✅ All user scenarios have integration test tasks  
✅ Tests come before implementation (TDD enforced)  
✅ Parallel tasks target different files  
✅ Dependencies clearly mapped  
✅ Exact file paths specified for each task  
✅ AI workflow requirements included  
✅ Real-time WebSocket functionality covered  

## Validation Checklist
- [x] All contracts have corresponding tests
- [x] All entities have model tasks  
- [x] All tests come before implementation
- [x] Parallel tasks truly independent
- [x] Each task specifies exact file path
- [x] No task modifies same file as another [P] task
- [x] AI workflow and real-time features included
- [x] Dashboard UI and CLI components covered
