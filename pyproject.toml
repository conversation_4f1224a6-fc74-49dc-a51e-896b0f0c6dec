[project]
name = "backlogger"
version = "0.1.0"
description = "Smart AI-Integrated Backlog Management System"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "dagster>=1.11.9",
    "fastapi>=0.116.1",
    "httpx>=0.28.1",
    "jinja2>=3.1.6",
    "pytest>=8.4.2",
    "sqlalchemy>=2.0.43",
    "uvicorn>=0.35.0",
    "websockets>=15.0.1",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/backlogger"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  \.git
  | \.venv
  | \.mypy_cache
  | \.pytest_cache
)/
'''

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
strict_optional = true
show_error_codes = true

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [".git", ".venv", "__pycache__", ".mypy_cache", ".pytest_cache"]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "flake8>=7.3.0",
    "mypy>=1.17.1",
]

[tool.pytest.ini_options]
pythonpath = ["src"]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "performance: marks tests as performance tests",
    "load: marks tests as load tests",
]
