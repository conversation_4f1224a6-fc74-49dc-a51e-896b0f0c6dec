"""Integration test for error handling edge cases."""

import pytest
import time
import threading
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from backlogger.main import app


@pytest.fixture
def client():
    """Create test client for integration testing."""
    return TestClient(app)


@pytest.fixture
def error_test_data():
    """Test data for error handling scenarios."""
    return {
        "valid_project": {
            "name": "Error Test Project",
            "description": "Project for testing error handling"
        },
        "valid_epic": {
            "name": "Error Test Epic",
            "description": "Epic for testing error handling"
        },
        "valid_feature": {
            "name": "Error Test Feature",
            "description": "Feature for testing error handling"
        },
        "valid_backlog_item": {
            "name": "Error Test Backlog Item",
            "description": "Backlog item for testing error handling"
        }
    }


class TestErrorHandlingEdgeCases:
    """Integration tests for error handling edge cases."""

    def test_database_connection_failures(self, client, error_test_data):
        """Test handling of database connection failures."""
        # Simulate database connection failure during project creation
        with patch('backlogger.core.database.get_db_session') as mock_db:
            mock_db.side_effect = Exception("Database connection failed")
            
            response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
            assert response.status_code == 500
            
            error_data = response.json()
            assert "detail" in error_data
            # Should not expose internal database details
            assert "Database connection failed" not in error_data["detail"]
            assert "internal server error" in error_data["detail"].lower()
    
    def test_database_timeout_scenarios(self, client, error_test_data):
        """Test handling of database timeout scenarios."""
        # Create project first
        project_response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
        assert project_response.status_code == 201
        project_id = project_response.json()["id"]
        
        # Simulate database timeout during query
        with patch('backlogger.core.project_service.ProjectService.get_projects') as mock_get:
            import asyncio
            mock_get.side_effect = asyncio.TimeoutError("Database query timeout")
            
            response = client.get("/api/v1/projects")
            assert response.status_code == 503  # Service Unavailable
            
            error_data = response.json()
            assert "timeout" in error_data["detail"].lower()

    def test_concurrent_modification_conflicts(self, client, error_test_data):
        """Test handling of concurrent modification conflicts."""
        # Create project
        project_response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
        assert project_response.status_code == 201
        project_id = project_response.json()["id"]
        
        # Simulate concurrent modifications
        def update_project_1():
            update_data = {"name": "Updated by User 1", "description": "Updated description 1"}
            return client.put(f"/api/v1/projects/{project_id}", json=update_data)
        
        def update_project_2():
            time.sleep(0.1)  # Slight delay to simulate race condition
            update_data = {"name": "Updated by User 2", "description": "Updated description 2"}
            return client.put(f"/api/v1/projects/{project_id}", json=update_data)
        
        # Execute concurrent updates
        results = []
        threads = []
        
        for update_func in [update_project_1, update_project_2]:
            thread = threading.Thread(target=lambda: results.append(update_func()))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Both should succeed or one should fail with conflict error
        success_count = sum(1 for result in results if result.status_code == 200)
        conflict_count = sum(1 for result in results if result.status_code == 409)
        
        # Either both succeed (if no conflict detection) or one conflicts
        assert success_count >= 1
        assert success_count + conflict_count == 2

    def test_foreign_key_constraint_violations(self, client, error_test_data):
        """Test handling of foreign key constraint violations."""
        # Create project and epic
        project_response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
        project_id = project_response.json()["id"]
        
        epic_response = client.post(f"/api/v1/projects/{project_id}/epics", json=error_test_data["valid_epic"])
        epic_id = epic_response.json()["id"]
        
        # Try to delete project with existing epics
        with patch('backlogger.core.project_service.ProjectService.delete_project') as mock_delete:
            from backlogger.core.exceptions import ConstraintViolationError
            mock_delete.side_effect = ConstraintViolationError("Cannot delete project with existing epics")
            
            response = client.delete(f"/api/v1/projects/{project_id}")
            assert response.status_code == 400
            
            error_data = response.json()
            assert "constraint" in error_data["detail"].lower() or "existing" in error_data["detail"].lower()

    def test_ai_workflow_service_failures(self, client, error_test_data):
        """Test handling of AI workflow service failures."""
        # Create complete hierarchy
        project_response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
        project_id = project_response.json()["id"]
        
        epic_response = client.post(f"/api/v1/projects/{project_id}/epics", json=error_test_data["valid_epic"])
        epic_id = epic_response.json()["id"]
        
        feature_response = client.post(f"/api/v1/epics/{epic_id}/features", json=error_test_data["valid_feature"])
        feature_id = feature_response.json()["id"]
        
        backlog_item_response = client.post(f"/api/v1/features/{feature_id}/backlog-items", json=error_test_data["valid_backlog_item"])
        backlog_item_id = backlog_item_response.json()["id"]
        
        # Test Dagster service unavailable
        with patch('backlogger.ai_workflows.workflow_manager.start_workflow') as mock_start:
            from backlogger.core.exceptions import ExternalServiceError
            mock_start.side_effect = ExternalServiceError("Dagster service is unavailable")
            
            response = client.post(f"/api/v1/backlog-items/{backlog_item_id}/ready-for-ai")
            assert response.status_code == 503
            
            error_data = response.json()
            assert "service" in error_data["detail"].lower() and "unavailable" in error_data["detail"].lower()
        
        # Test AI workflow quota exceeded
        with patch('backlogger.ai_workflows.workflow_manager.start_workflow') as mock_start:
            from backlogger.core.exceptions import QuotaExceededError
            mock_start.side_effect = QuotaExceededError("AI workflow quota exceeded")
            
            response = client.post(f"/api/v1/backlog-items/{backlog_item_id}/ready-for-ai")
            assert response.status_code == 429
            
            error_data = response.json()
            assert "quota" in error_data["detail"].lower()

    def test_websocket_connection_errors(self, client):
        """Test WebSocket connection error scenarios."""
        # Test connection without proper authentication (if required)
        try:
            with client.websocket_connect("/ws/dashboard") as websocket:
                # Test sending malformed message
                websocket.send_text("invalid json")
                
                try:
                    response = websocket.receive_text()
                    error_data = json.loads(response)
                    assert error_data["type"] == "error"
                except:
                    # Expected for malformed JSON
                    pass
        except Exception as e:
            # Connection might fail if authentication is required
            assert "authentication" in str(e).lower() or "unauthorized" in str(e).lower()
        
        # Test connection with invalid endpoint
        try:
            with client.websocket_connect("/ws/invalid") as websocket:
                pass
        except Exception:
            # Expected for invalid endpoint
            pass

    def test_memory_exhaustion_scenarios(self, client, error_test_data):
        """Test handling of memory exhaustion scenarios."""
        # Create project
        project_response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
        project_id = project_response.json()["id"]
        
        # Simulate memory exhaustion during large query
        with patch('backlogger.core.project_service.ProjectService.get_projects') as mock_get:
            mock_get.side_effect = MemoryError("Memory allocation failed")
            
            response = client.get("/api/v1/projects")
            assert response.status_code == 500
            
            error_data = response.json()
            assert "server error" in error_data["detail"].lower()
            # Should not expose memory details
            assert "memory" not in error_data["detail"].lower()

    def test_invalid_json_content_length(self, client):
        """Test handling of invalid content length scenarios."""
        # Test with content-length mismatch
        headers = {
            "Content-Type": "application/json",
            "Content-Length": "100"  # Incorrect length
        }
        
        short_json = '{"name": "Test"}'
        
        try:
            response = client.post("/api/v1/projects", data=short_json, headers=headers)
            # Behavior depends on server implementation
            assert response.status_code in [400, 422, 500]
        except Exception:
            # Expected for malformed request
            pass

    def test_cascade_failure_scenarios(self, client, error_test_data):
        """Test cascade failure scenarios across the system."""
        # Create hierarchy
        project_response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
        project_id = project_response.json()["id"]
        
        epic_response = client.post(f"/api/v1/projects/{project_id}/epics", json=error_test_data["valid_epic"])
        epic_id = epic_response.json()["id"]
        
        # Simulate failure during epic creation that affects project
        with patch('backlogger.core.epic_service.EpicService.create_epic') as mock_create:
            mock_create.side_effect = Exception("Epic creation failed")
            
            response = client.post(f"/api/v1/projects/{project_id}/epics", json=error_test_data["valid_epic"])
            assert response.status_code == 500
            
            # Verify project is still in consistent state
            project_get_response = client.get(f"/api/v1/projects/{project_id}")
            assert project_get_response.status_code == 200

    def test_partial_failure_recovery(self, client, error_test_data):
        """Test recovery from partial failures."""
        # Create project
        project_response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
        project_id = project_response.json()["id"]
        
        # Simulate partial failure during bulk operation
        epic_data_list = [
            {"name": "Epic 1", "description": "First epic"},
            {"name": "Epic 2", "description": "Second epic"},
            {"name": "Epic 3", "description": "Third epic"}
        ]
        
        successful_creates = 0
        failed_creates = 0
        
        for i, epic_data in enumerate(epic_data_list):
            if i == 1:  # Simulate failure on second epic
                with patch('backlogger.core.epic_service.EpicService.create_epic') as mock_create:
                    mock_create.side_effect = Exception("Simulated failure")
                    response = client.post(f"/api/v1/projects/{project_id}/epics", json=epic_data)
                    if response.status_code != 201:
                        failed_creates += 1
            else:
                response = client.post(f"/api/v1/projects/{project_id}/epics", json=epic_data)
                if response.status_code == 201:
                    successful_creates += 1
        
        # Verify partial success
        assert successful_creates >= 1
        assert failed_creates >= 1
        
        # Verify system is still functional
        epics_response = client.get(f"/api/v1/projects/{project_id}/epics")
        assert epics_response.status_code == 200

    def test_resource_exhaustion_limits(self, client, error_test_data):
        """Test handling of resource exhaustion scenarios."""
        # Test with excessive request size
        large_description = "x" * 10000  # Very large description
        large_project_data = {
            "name": "Large Project",
            "description": large_description
        }
        
        response = client.post("/api/v1/projects", json=large_project_data)
        # Should either succeed or fail with appropriate error
        assert response.status_code in [201, 413, 422]  # Created, Payload Too Large, or Validation Error
        
        if response.status_code == 413:
            error_data = response.json()
            assert "too large" in error_data["detail"].lower()

    def test_error_response_consistency(self, client, error_test_data):
        """Test that error responses follow consistent format."""
        error_scenarios = [
            # Not found
            ("GET", "/api/v1/projects/99999", 404),
            # Validation error
            ("POST", "/api/v1/projects", 422, {}),
            # Method not allowed
            ("PATCH", "/api/v1/projects", 405),
        ]
        
        for scenario in error_scenarios:
            if len(scenario) == 3:
                method, url, expected_status = scenario
                json_data = None
            else:
                method, url, expected_status, json_data = scenario
            
            if method == "GET":
                response = client.get(url)
            elif method == "POST":
                response = client.post(url, json=json_data)
            elif method == "PATCH":
                response = client.patch(url)
            
            assert response.status_code == expected_status
            
            # All error responses should have consistent structure
            error_data = response.json()
            assert "detail" in error_data
            assert isinstance(error_data["detail"], (str, list))

    def test_transaction_rollback_scenarios(self, client, error_test_data):
        """Test transaction rollback in error scenarios."""
        # Create project
        project_response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
        project_id = project_response.json()["id"]
        
        # Simulate failure that should trigger rollback
        with patch('backlogger.core.epic_service.EpicService.create_epic') as mock_create:
            # Mock partial success then failure
            def side_effect_func(*args, **kwargs):
                # First call succeeds, second fails
                if not hasattr(side_effect_func, 'call_count'):
                    side_effect_func.call_count = 0
                side_effect_func.call_count += 1
                
                if side_effect_func.call_count == 1:
                    return {
                        "id": 1,
                        "name": "Test Epic",
                        "description": "Test description",
                        "status": "todo",
                        "project_id": project_id
                    }
                else:
                    raise Exception("Simulated database error")
            
            mock_create.side_effect = side_effect_func
            
            # First epic creation should work
            response1 = client.post(f"/api/v1/projects/{project_id}/epics", json=error_test_data["valid_epic"])
            assert response1.status_code == 201
            
            # Second should fail
            response2 = client.post(f"/api/v1/projects/{project_id}/epics", json=error_test_data["valid_epic"])
            assert response2.status_code == 500
        
        # Verify data consistency after rollback
        epics_response = client.get(f"/api/v1/projects/{project_id}/epics")
        assert epics_response.status_code == 200

    def test_error_logging_and_monitoring(self, client, error_test_data):
        """Test that errors are properly logged for monitoring."""
        # Simulate error that should be logged
        with patch('backlogger.core.logging.logger') as mock_logger:
            with patch('backlogger.core.project_service.ProjectService.create_project') as mock_create:
                mock_create.side_effect = Exception("Simulated error for logging test")
                
                response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
                assert response.status_code == 500
                
                # Verify error was logged
                mock_logger.error.assert_called()
                
                # Verify sensitive information is not in logs
                log_calls = mock_logger.error.call_args_list
                for call in log_calls:
                    log_message = str(call)
                    # Should not contain sensitive data like passwords, tokens, etc.
                    assert "password" not in log_message.lower()
                    assert "token" not in log_message.lower()

    def test_graceful_degradation(self, client, error_test_data):
        """Test graceful degradation when optional services fail."""
        # Create hierarchy
        project_response = client.post("/api/v1/projects", json=error_test_data["valid_project"])
        project_id = project_response.json()["id"]
        
        epic_response = client.post(f"/api/v1/projects/{project_id}/epics", json=error_test_data["valid_epic"])
        epic_id = epic_response.json()["id"]
        
        feature_response = client.post(f"/api/v1/epics/{epic_id}/features", json=error_test_data["valid_feature"])
        feature_id = feature_response.json()["id"]
        
        backlog_item_response = client.post(f"/api/v1/features/{feature_id}/backlog-items", json=error_test_data["valid_backlog_item"])
        backlog_item_id = backlog_item_response.json()["id"]
        
        # Test that core functionality works even when AI service fails
        with patch('backlogger.ai_workflows.workflow_manager.start_workflow') as mock_start:
            mock_start.side_effect = Exception("AI service unavailable")
            
            # AI workflow should fail
            ai_response = client.post(f"/api/v1/backlog-items/{backlog_item_id}/ready-for-ai")
            assert ai_response.status_code in [500, 503]
            
            # But core CRUD operations should still work
            update_response = client.put(f"/api/v1/backlog-items/{backlog_item_id}", json={"name": "Updated Name"})
            assert update_response.status_code == 200
            
            # And getting status should still work
            status_response = client.get(f"/api/v1/backlog-items/{backlog_item_id}")
            assert status_response.status_code == 200
