"""Database configuration and connection management."""

import os
from contextlib import contextmanager
from typing import Generator

from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, Session, declarative_base

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./backlogger.db")

# SQLAlchemy setup
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {},
    echo=os.getenv("SQL_DEBUG", "false").lower() == "true"
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for all models
Base = declarative_base()

# Metadata for schema operations
metadata = MetaData()


def get_database_session() -> Generator[Session, None, None]:
    """Get a database session.
    
    This function is designed to be used as a dependency in FastAPI endpoints.
    It creates a session, yields it, and ensures it's closed after use.
    
    Yields:
        Session: SQLAlchemy database session
        
    Example:
        ```python
        from fastapi import Depends
        
        @app.get("/projects")
        def get_projects(db: Session = Depends(get_database_session)):
            return db.query(Project).all()
        ```
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Alias for compatibility
get_db = get_database_session


@contextmanager
def get_database_session_context() -> Generator[Session, None, None]:
    """Get a database session for use in context managers.
    
    This is useful for non-FastAPI code that needs database access.
    
    Yields:
        Session: SQLAlchemy database session
        
    Example:
        ```python
        with get_database_session_context() as db:
            project = Project(name="Test Project")
            db.add(project)
            db.commit()
        ```
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()


def init_database() -> None:
    """Initialize the database by creating all tables.
    
    This function should be called during application startup to ensure
    all tables are created. It's safe to call multiple times.
    """
    # Import all models here to ensure they're registered with Base
    from ..models import Project, Epic, Feature, BacklogItem, Task
    
    # Create all tables
    Base.metadata.create_all(bind=engine)


def drop_database() -> None:
    """Drop all tables from the database.
    
    WARNING: This will delete all data! Only use in tests or development.
    """
    Base.metadata.drop_all(bind=engine)


def reset_database() -> None:
    """Reset the database by dropping and recreating all tables.
    
    WARNING: This will delete all data! Only use in tests or development.
    """
    drop_database()
    init_database()
