"""Project service with CRUD operations."""

from datetime import datetime
from typing import List, Optional

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from ..models.project import Project, ProjectStatus
from ..core.database import get_db


class ProjectService:
    """Service class for project CRUD operations and business logic."""
    
    def __init__(self, db: Session):
        """Initialize service with database session."""
        self.db = db
    
    def create_project(self, name: str, description: Optional[str] = None) -> Project:
        """Create a new project."""
        # Validate input
        if not name or not name.strip():
            raise ValueError("Project name is required")
        
        if len(name) > 200:
            raise ValueError("Project name cannot exceed 200 characters")
        
        if description and len(description) > 2000:
            raise ValueError("Project description cannot exceed 2000 characters")
        
        # Check for duplicate name
        existing = self.get_project_by_name(name.strip())
        if existing:
            raise ValueError(f"Project with name '{name}' already exists")
        
        # Create project
        project = Project(
            name=name.strip(),
            description=description.strip() if description else None,
            status=ProjectStatus.ACTIVE
        )
        
        try:
            self.db.add(project)
            self.db.commit()
            self.db.refresh(project)
            return project
        except IntegrityError:
            self.db.rollback()
            raise ValueError(f"Project with name '{name}' already exists")
    
    def get_project_by_id(self, project_id: int) -> Optional[Project]:
        """Get project by ID."""
        return self.db.query(Project).filter(Project.id == project_id).first()
    
    def get_project_by_name(self, name: str) -> Optional[Project]:
        """Get project by name."""
        return self.db.query(Project).filter(Project.name == name).first()
    
    def list_projects(
        self,
        status: Optional[ProjectStatus] = None,
        limit: int = 50,
        offset: int = 0
    ) -> tuple[List[Project], int]:
        """List projects with optional filtering and pagination."""
        query = self.db.query(Project)

        if status:
            query = query.filter(Project.status == status)

        # Get total count
        total = query.count()

        # Apply pagination
        projects = query.offset(offset).limit(limit).all()

        return projects, total

    def get_projects_paginated(
        self,
        page: int = 1,
        limit: int = 12,
        filters: Optional[dict] = None
    ) -> dict:
        """Get projects with pagination for dashboard views."""
        if filters is None:
            filters = {}

        # Calculate offset
        offset = (page - 1) * limit

        # Build query
        query = self.db.query(Project)

        # Apply filters
        if 'status' in filters:
            query = query.filter(Project.status == filters['status'])

        if 'search' in filters and filters['search']:
            search_term = f"%{filters['search']}%"
            query = query.filter(
                Project.name.ilike(search_term) |
                Project.description.ilike(search_term)
            )

        # Get total count
        total = query.count()

        # Apply pagination and ordering
        projects = query.order_by(Project.updated_at.desc()).offset(offset).limit(limit).all()

        # Calculate pagination info
        total_pages = (total + limit - 1) // limit

        return {
            'projects': projects,
            'total': total,
            'total_pages': total_pages,
            'current_page': page,
            'limit': limit,
            'offset': offset
        }
    
    def update_project(
        self,
        project_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        status: Optional[ProjectStatus] = None
    ) -> Optional[Project]:
        """Update an existing project."""
        project = self.get_project_by_id(project_id)
        if not project:
            return None
        
        # Validate inputs
        if name is not None:
            if not name.strip():
                raise ValueError("Project name cannot be empty")
            if len(name) > 200:
                raise ValueError("Project name cannot exceed 200 characters")
            
            # Check for duplicate name (excluding current project)
            existing = self.db.query(Project).filter(
                Project.name == name.strip(),
                Project.id != project_id
            ).first()
            if existing:
                raise ValueError(f"Project with name '{name}' already exists")
            
            project.name = name.strip()
        
        if description is not None:
            if len(description) > 2000:
                raise ValueError("Project description cannot exceed 2000 characters")
            project.description = description.strip() if description else None
        
        if status is not None:
            if not project.can_transition_to(status):
                raise ValueError(f"Cannot transition project from '{project.status}' to '{status}'")
            project.status = status
        
        # Note: updated_at will be set automatically by the onupdate=func.now() in the model
        
        try:
            self.db.commit()
            self.db.refresh(project)
            return project
        except IntegrityError:
            self.db.rollback()
            raise ValueError("Failed to update project due to database constraint")
    
    def update_project_status(self, project_id: int, status: ProjectStatus) -> Optional[Project]:
        """Update project status."""
        project = self.get_project_by_id(project_id)
        if not project:
            return None
        
        if not project.can_transition_to(status):
            raise ValueError(f"Cannot transition project from '{project.status}' to '{status}'")
        
        project.status = status
        # Note: updated_at will be set automatically by the onupdate=func.now() in the model
        
        try:
            self.db.commit()
            self.db.refresh(project)
            return project
        except IntegrityError:
            self.db.rollback()
            raise ValueError("Failed to update project status due to database constraint")
    
    def delete_project(self, project_id: int) -> bool:
        """Delete a project if it has no active epics."""
        project = self.get_project_by_id(project_id)
        if not project:
            return False
        
        if not project.can_be_deleted():
            raise ValueError("Cannot delete project with active epics")
        
        try:
            self.db.delete(project)
            self.db.commit()
            return True
        except Exception:
            self.db.rollback()
            raise
    
    def get_project_with_epics(self, project_id: int) -> Optional[Project]:
        """Get project with its epics loaded."""
        from sqlalchemy.orm import joinedload
        
        return self.db.query(Project).options(
            joinedload(Project.epics)
        ).filter(Project.id == project_id).first()
    
    def get_project_stats(self, project_id: int) -> Optional[dict]:
        """Get project statistics."""
        project = self.get_project_by_id(project_id)
        if not project:
            return None
        
        from ..models.epic import WorkItemStatus
        
        epic_stats = {
            "total": len(project.epics),
            "todo": len([e for e in project.epics if e.status == WorkItemStatus.TODO]),
            "in_progress": len([e for e in project.epics if e.status == WorkItemStatus.IN_PROGRESS]),
            "done": len([e for e in project.epics if e.status == WorkItemStatus.DONE])
        }
        
        return {
            "id": project.id,
            "name": project.name,
            "status": project.status,
            "epics": epic_stats,
            "created_at": project.created_at,
            "updated_at": project.updated_at
        }


def get_project_service(db: Optional[Session] = None) -> ProjectService:
    """Get project service instance."""
    if db is None:
        db = next(get_db())
    return ProjectService(db)
