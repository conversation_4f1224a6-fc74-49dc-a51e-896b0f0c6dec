"""Epic service with status transitions."""

from datetime import datetime
from typing import List, Optional

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from ..models.epic import Epic, WorkItemStatus
from ..models.project import Project
from ..core.database import get_db


class EpicService:
    """Service class for epic CRUD operations and business logic."""
    
    def __init__(self, db: Session):
        """Initialize service with database session."""
        self.db = db
    
    def create_epic(
        self,
        name: str,
        project_id: int,
        description: Optional[str] = None,
        assignee: Optional[str] = None
    ) -> Epic:
        """Create a new epic."""
        # Validate input
        if not name or not name.strip():
            raise ValueError("Epic name is required")
        
        if len(name) > 200:
            raise ValueError("Epic name cannot exceed 200 characters")
        
        if description and len(description) > 2000:
            raise ValueError("Epic description cannot exceed 2000 characters")
        
        # Validate project exists
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise ValueError(f"Project with ID {project_id} not found")
        
        # Create epic
        epic = Epic(
            name=name.strip(),
            description=description.strip() if description else None,
            project_id=project_id,
            assignee=assignee.strip() if assignee else None,
            status=WorkItemStatus.TODO
        )
        
        try:
            self.db.add(epic)
            self.db.commit()
            self.db.refresh(epic)
            return epic
        except IntegrityError:
            self.db.rollback()
            raise ValueError("Failed to create epic due to database constraint")
    
    def get_epic_by_id(self, epic_id: int) -> Optional[Epic]:
        """Get epic by ID."""
        return self.db.query(Epic).filter(Epic.id == epic_id).first()
    
    def list_epics_by_project(
        self,
        project_id: int,
        status: Optional[WorkItemStatus] = None,
        limit: int = 50,
        offset: int = 0
    ) -> tuple[List[Epic], int]:
        """List epics for a project with optional filtering."""
        query = self.db.query(Epic).filter(Epic.project_id == project_id)

        if status:
            query = query.filter(Epic.status == status)

        # Get total count
        total = query.count()

        # Apply pagination
        epics = query.offset(offset).limit(limit).all()

        return epics, total

    def get_epics_by_project(self, project_id: int) -> List[Epic]:
        """Get all epics for a project (dashboard helper method)."""
        return self.db.query(Epic).filter(Epic.project_id == project_id).order_by(Epic.created_at.desc()).all()
    
    def update_epic(
        self,
        epic_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        status: Optional[WorkItemStatus] = None,
        assignee: Optional[str] = None
    ) -> Optional[Epic]:
        """Update an existing epic."""
        epic = self.get_epic_by_id(epic_id)
        if not epic:
            return None
        
        # Validate inputs
        if name is not None:
            if not name.strip():
                raise ValueError("Epic name cannot be empty")
            if len(name) > 200:
                raise ValueError("Epic name cannot exceed 200 characters")
            epic.name = name.strip()
        
        if description is not None:
            if len(description) > 2000:
                raise ValueError("Epic description cannot exceed 2000 characters")
            epic.description = description.strip() if description else None
        
        # Handle status and assignee changes together
        new_status = status if status is not None else epic.status
        new_assignee = assignee if assignee is not None else epic.assignee
        
        if status is not None:
            if not epic.validate_status_change(new_status, new_assignee):
                if not epic.can_transition_to(new_status):
                    raise ValueError(f"Cannot transition epic from '{epic.status}' to '{new_status}'")
                if epic.requires_assignee(new_status) and not new_assignee:
                    raise ValueError(f"Status '{new_status}' requires an assignee")
                if new_status == WorkItemStatus.DONE and not epic.can_complete():
                    raise ValueError("Cannot complete epic with incomplete features")
            
            epic.status = new_status
        
        if assignee is not None:
            if epic.requires_assignee(epic.status) and not assignee:
                raise ValueError(f"Status '{epic.status}' requires an assignee")
            epic.assignee = assignee.strip() if assignee else None
        
        # Note: updated_at will be set automatically by the onupdate=func.now() in the model
        
        try:
            self.db.commit()
            self.db.refresh(epic)
            return epic
        except IntegrityError:
            self.db.rollback()
            raise ValueError("Failed to update epic due to database constraint")
    
    def delete_epic(self, epic_id: int) -> bool:
        """Delete an epic and all its features."""
        epic = self.get_epic_by_id(epic_id)
        if not epic:
            return False
        
        try:
            self.db.delete(epic)
            self.db.commit()
            return True
        except Exception:
            self.db.rollback()
            raise
    
    def get_epic_with_features(self, epic_id: int) -> Optional[Epic]:
        """Get epic with its features loaded."""
        from sqlalchemy.orm import joinedload
        
        return self.db.query(Epic).options(
            joinedload(Epic.features)
        ).filter(Epic.id == epic_id).first()
    
    def transition_status(self, epic_id: int, new_status: WorkItemStatus, assignee: Optional[str] = None) -> Epic:
        """Transition epic status with validation."""
        epic = self.get_epic_by_id(epic_id)
        if not epic:
            raise ValueError(f"Epic with ID {epic_id} not found")
        
        if not epic.validate_status_change(new_status, assignee):
            if not epic.can_transition_to(new_status):
                raise ValueError(f"Cannot transition epic from '{epic.status}' to '{new_status}'")
            if epic.requires_assignee(new_status) and not assignee:
                raise ValueError(f"Status '{new_status}' requires an assignee")
            if new_status == WorkItemStatus.DONE and not epic.can_complete():
                raise ValueError("Cannot complete epic with incomplete features")
        
        epic.status = new_status
        if assignee is not None:
            epic.assignee = assignee
        elif epic.requires_assignee(new_status) and not epic.assignee:
            raise ValueError(f"Status '{new_status}' requires an assignee")
        
        # Note: updated_at will be set automatically by the onupdate=func.now() in the model
        
        try:
            self.db.commit()
            self.db.refresh(epic)
            return epic
        except Exception:
            self.db.rollback()
            raise
    
    def update_epic_status(self, epic_id: int, new_status: WorkItemStatus, assignee: Optional[str] = None) -> Epic:
        """Update epic status (alias for transition_status)."""
        return self.transition_status(epic_id, new_status, assignee)
    
    def get_epic_stats(self, epic_id: int) -> Optional[dict]:
        """Get epic statistics."""
        epic = self.get_epic_by_id(epic_id)
        if not epic:
            return None
        
        feature_stats = {
            "total": len(epic.features),
            "todo": len([f for f in epic.features if f.status == WorkItemStatus.TODO]),
            "in_progress": len([f for f in epic.features if f.status == WorkItemStatus.IN_PROGRESS]),
            "done": len([f for f in epic.features if f.status == WorkItemStatus.DONE])
        }
        
        return {
            "id": epic.id,
            "name": epic.name,
            "status": epic.status,
            "assignee": epic.assignee,
            "features": feature_stats,
            "created_at": epic.created_at,
            "updated_at": epic.updated_at
        }


def get_epic_service(db: Optional[Session] = None) -> EpicService:
    """Get epic service instance."""
    if db is None:
        db = next(get_db())
    return EpicService(db)
