"""Epic and feature endpoints with validation."""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field, ConfigDict
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..core.epic_service import EpicService
from ..core.feature_service import FeatureService
from ..models.epic import WorkItemStatus


# Pydantic models for epics
class EpicCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200, description="Epic name")
    description: Optional[str] = Field(None, max_length=2000, description="Epic description")
    assignee: Optional[str] = Field(None, max_length=100, description="Epic assignee")


class EpicUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Epic name")
    description: Optional[str] = Field(None, max_length=2000, description="Epic description")
    status: Optional[WorkItemStatus] = Field(None, description="Epic status")
    assignee: Optional[str] = Field(None, max_length=100, description="Epic assignee")


class EpicResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    status: WorkItemStatus
    assignee: Optional[str]
    project_id: int
    created_at: str
    updated_at: str
    
    model_config = ConfigDict(from_attributes=True)


class EpicListResponse(BaseModel):
    epics: List[EpicResponse]
    total: int
    limit: int
    offset: int


# Pydantic models for features
class FeatureCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200, description="Feature name")
    description: Optional[str] = Field(None, max_length=2000, description="Feature description")
    assignee: Optional[str] = Field(None, max_length=100, description="Feature assignee")


class FeatureUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Feature name")
    description: Optional[str] = Field(None, max_length=2000, description="Feature description")
    status: Optional[WorkItemStatus] = Field(None, description="Feature status")
    assignee: Optional[str] = Field(None, max_length=100, description="Feature assignee")


class FeatureResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    status: WorkItemStatus
    assignee: Optional[str]
    epic_id: int
    created_at: str
    updated_at: str
    
    model_config = ConfigDict(from_attributes=True)


class FeatureListResponse(BaseModel):
    features: List[FeatureResponse]
    total: int
    limit: int
    offset: int


class StatusTransition(BaseModel):
    status: WorkItemStatus
    assignee: Optional[str] = None


# Create router
router = APIRouter(tags=["work-items"])


def get_epic_service(db: Session = Depends(get_db)) -> EpicService:
    """Dependency to get epic service."""
    return EpicService(db)


def get_feature_service(db: Session = Depends(get_db)) -> FeatureService:
    """Dependency to get feature service."""
    return FeatureService(db)


# Epic endpoints
@router.get("/projects/{project_id}/epics", response_model=EpicListResponse)
async def list_epics(
    project_id: int,
    status: Optional[WorkItemStatus] = Query(None, description="Filter by epic status"),
    limit: int = Query(50, ge=1, le=100, description="Number of epics to return"),
    offset: int = Query(0, ge=0, description="Number of epics to skip"),
    service: EpicService = Depends(get_epic_service)
):
    """List epics for a project with optional filtering."""
    try:
        epics, total = service.list_epics_by_project(
            project_id=project_id,
            status=status,
            limit=limit,
            offset=offset
        )
        
        return EpicListResponse(
            epics=[
                EpicResponse(
                    id=e.id,
                    name=e.name,
                    description=e.description,
                    status=e.status,
                    assignee=e.assignee,
                    project_id=e.project_id,
                    created_at=e.created_at.isoformat(),
                    updated_at=e.updated_at.isoformat()
                ) for e in epics
            ],
            total=total,
            limit=limit,
            offset=offset
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/projects/{project_id}/epics", response_model=EpicResponse, status_code=201)
async def create_epic(
    project_id: int,
    epic_data: EpicCreate,
    service: EpicService = Depends(get_epic_service)
):
    """Create a new epic for a project."""
    try:
        epic = service.create_epic(
            name=epic_data.name,
            project_id=project_id,
            description=epic_data.description,
            assignee=epic_data.assignee
        )
        
        return EpicResponse(
            id=epic.id,
            name=epic.name,
            description=epic.description,
            status=epic.status,
            assignee=epic.assignee,
            project_id=epic.project_id,
            created_at=epic.created_at.isoformat(),
            updated_at=epic.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/epics/{epic_id}", response_model=EpicResponse)
async def get_epic(
    epic_id: int,
    service: EpicService = Depends(get_epic_service)
):
    """Get epic details."""
    try:
        epic = service.get_epic_by_id(epic_id)
        
        if not epic:
            raise HTTPException(status_code=404, detail="Epic not found")
        
        return EpicResponse(
            id=epic.id,
            name=epic.name,
            description=epic.description,
            status=epic.status,
            assignee=epic.assignee,
            project_id=epic.project_id,
            created_at=epic.created_at.isoformat(),
            updated_at=epic.updated_at.isoformat()
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/epics/{epic_id}", response_model=EpicResponse)
async def update_epic(
    epic_id: int,
    epic_data: EpicUpdate,
    service: EpicService = Depends(get_epic_service)
):
    """Update an existing epic."""
    try:
        epic = service.update_epic(
            epic_id=epic_id,
            name=epic_data.name,
            description=epic_data.description,
            status=epic_data.status,
            assignee=epic_data.assignee
        )
        
        if not epic:
            raise HTTPException(status_code=404, detail="Epic not found")
        
        return EpicResponse(
            id=epic.id,
            name=epic.name,
            description=epic.description,
            status=epic.status,
            assignee=epic.assignee,
            project_id=epic.project_id,
            created_at=epic.created_at.isoformat(),
            updated_at=epic.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/epics/{epic_id}", status_code=204)
async def delete_epic(
    epic_id: int,
    service: EpicService = Depends(get_epic_service)
):
    """Delete an epic."""
    try:
        success = service.delete_epic(epic_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Epic not found")
        
        return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/epics/{epic_id}/transition", response_model=EpicResponse)
async def transition_epic_status(
    epic_id: int,
    transition: StatusTransition,
    service: EpicService = Depends(get_epic_service)
):
    """Transition epic status."""
    try:
        epic = service.transition_status(
            epic_id=epic_id,
            new_status=transition.status,
            assignee=transition.assignee
        )
        
        return EpicResponse(
            id=epic.id,
            name=epic.name,
            description=epic.description,
            status=epic.status,
            assignee=epic.assignee,
            project_id=epic.project_id,
            created_at=epic.created_at.isoformat(),
            updated_at=epic.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Feature endpoints
@router.get("/epics/{epic_id}/features", response_model=FeatureListResponse)
async def list_features(
    epic_id: int,
    status: Optional[WorkItemStatus] = Query(None, description="Filter by feature status"),
    limit: int = Query(50, ge=1, le=100, description="Number of features to return"),
    offset: int = Query(0, ge=0, description="Number of features to skip"),
    service: FeatureService = Depends(get_feature_service)
):
    """List features for an epic with optional filtering."""
    try:
        features, total = service.list_features_by_epic(
            epic_id=epic_id,
            status=status,
            limit=limit,
            offset=offset
        )
        
        return FeatureListResponse(
            features=[
                FeatureResponse(
                    id=f.id,
                    name=f.name,
                    description=f.description,
                    status=f.status,
                    assignee=f.assignee,
                    epic_id=f.epic_id,
                    created_at=f.created_at.isoformat(),
                    updated_at=f.updated_at.isoformat()
                ) for f in features
            ],
            total=total,
            limit=limit,
            offset=offset
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/epics/{epic_id}/features", response_model=FeatureResponse, status_code=201)
async def create_feature(
    epic_id: int,
    feature_data: FeatureCreate,
    service: FeatureService = Depends(get_feature_service)
):
    """Create a new feature for an epic."""
    try:
        feature = service.create_feature(
            name=feature_data.name,
            epic_id=epic_id,
            description=feature_data.description,
            assignee=feature_data.assignee
        )
        
        return FeatureResponse(
            id=feature.id,
            name=feature.name,
            description=feature.description,
            status=feature.status,
            assignee=feature.assignee,
            epic_id=feature.epic_id,
            created_at=feature.created_at.isoformat(),
            updated_at=feature.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/features/{feature_id}", response_model=FeatureResponse)
async def get_feature(
    feature_id: int,
    service: FeatureService = Depends(get_feature_service)
):
    """Get feature details."""
    try:
        feature = service.get_feature_by_id(feature_id)
        
        if not feature:
            raise HTTPException(status_code=404, detail="Feature not found")
        
        return FeatureResponse(
            id=feature.id,
            name=feature.name,
            description=feature.description,
            status=feature.status,
            assignee=feature.assignee,
            epic_id=feature.epic_id,
            created_at=feature.created_at.isoformat(),
            updated_at=feature.updated_at.isoformat()
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/features/{feature_id}", response_model=FeatureResponse)
async def update_feature(
    feature_id: int,
    feature_data: FeatureUpdate,
    service: FeatureService = Depends(get_feature_service)
):
    """Update an existing feature."""
    try:
        feature = service.update_feature(
            feature_id=feature_id,
            name=feature_data.name,
            description=feature_data.description,
            status=feature_data.status,
            assignee=feature_data.assignee
        )
        
        if not feature:
            raise HTTPException(status_code=404, detail="Feature not found")
        
        return FeatureResponse(
            id=feature.id,
            name=feature.name,
            description=feature.description,
            status=feature.status,
            assignee=feature.assignee,
            epic_id=feature.epic_id,
            created_at=feature.created_at.isoformat(),
            updated_at=feature.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/features/{feature_id}", status_code=204)
async def delete_feature(
    feature_id: int,
    service: FeatureService = Depends(get_feature_service)
):
    """Delete a feature."""
    try:
        success = service.delete_feature(feature_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Feature not found")
        
        return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/features/{feature_id}/transition", response_model=FeatureResponse)
async def transition_feature_status(
    feature_id: int,
    transition: StatusTransition,
    service: FeatureService = Depends(get_feature_service)
):
    """Transition feature status."""
    try:
        feature = service.transition_status(
            feature_id=feature_id,
            new_status=transition.status,
            assignee=transition.assignee
        )
        
        return FeatureResponse(
            id=feature.id,
            name=feature.name,
            description=feature.description,
            status=feature.status,
            assignee=feature.assignee,
            epic_id=feature.epic_id,
            created_at=feature.created_at.isoformat(),
            updated_at=feature.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Export router for main app
__all__ = ["router"]
