"""BacklogItem and task endpoints."""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field, ConfigDict
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..core.backlog_service import BacklogService
from ..core.task_service import TaskService
from ..models.backlog_item import BacklogItemStatus
from ..models.epic import WorkItemStatus


# Pydantic models for backlog items
class BacklogItemCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200, description="Backlog item name")
    description: Optional[str] = Field(None, max_length=2000, description="Backlog item description")
    assignee: Optional[str] = Field(None, max_length=100, description="Backlog item assignee")


class BacklogItemUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Backlog item name")
    description: Optional[str] = Field(None, max_length=2000, description="Backlog item description")
    status: Optional[BacklogItemStatus] = Field(None, description="Backlog item status")
    assignee: Optional[str] = Field(None, max_length=100, description="Backlog item assignee")


class BacklogItemResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    status: BacklogItemStatus
    assignee: Optional[str]
    feature_id: int
    ai_workflow_id: Optional[str]
    created_at: str
    updated_at: str
    
    model_config = ConfigDict(from_attributes=True)


class BacklogItemListResponse(BaseModel):
    backlog_items: List[BacklogItemResponse]
    total: int
    limit: int
    offset: int


# Pydantic models for tasks
class TaskCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200, description="Task name")
    description: Optional[str] = Field(None, max_length=2000, description="Task description")
    processing_order: int = Field(..., gt=0, description="Task processing order")
    assignee: Optional[str] = Field(None, max_length=100, description="Task assignee")


class TaskUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Task name")
    description: Optional[str] = Field(None, max_length=2000, description="Task description")
    status: Optional[WorkItemStatus] = Field(None, description="Task status")
    assignee: Optional[str] = Field(None, max_length=100, description="Task assignee")
    processing_order: Optional[int] = Field(None, gt=0, description="Task processing order")


class TaskResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    status: WorkItemStatus
    assignee: Optional[str]
    backlog_item_id: int
    processing_order: int
    created_at: str
    updated_at: str
    
    model_config = ConfigDict(from_attributes=True)


class TaskListResponse(BaseModel):
    tasks: List[TaskResponse]


class BacklogItemStatusTransition(BaseModel):
    status: BacklogItemStatus
    assignee: Optional[str] = None


class TaskStatusTransition(BaseModel):
    status: WorkItemStatus
    assignee: Optional[str] = None


class TaskReorder(BaseModel):
    new_order: int = Field(..., gt=0, description="New processing order")


# Create router
router = APIRouter(tags=["backlog"])


def get_backlog_service(db: Session = Depends(get_db)) -> BacklogService:
    """Dependency to get backlog service."""
    return BacklogService(db)


def get_task_service(db: Session = Depends(get_db)) -> TaskService:
    """Dependency to get task service."""
    return TaskService(db)


# Backlog item endpoints
@router.get("/features/{feature_id}/backlog-items", response_model=BacklogItemListResponse)
async def list_backlog_items(
    feature_id: int,
    status: Optional[BacklogItemStatus] = Query(None, description="Filter by backlog item status"),
    limit: int = Query(50, ge=1, le=100, description="Number of items to return"),
    offset: int = Query(0, ge=0, description="Number of items to skip"),
    service: BacklogService = Depends(get_backlog_service)
):
    """List backlog items for a feature with optional filtering."""
    try:
        items, total = service.list_backlog_items_by_feature(
            feature_id=feature_id,
            status=status,
            limit=limit,
            offset=offset
        )
        
        return BacklogItemListResponse(
            backlog_items=[
                BacklogItemResponse(
                    id=item.id,
                    name=item.name,
                    description=item.description,
                    status=item.status,
                    assignee=item.assignee,
                    feature_id=item.feature_id,
                    ai_workflow_id=item.ai_workflow_id,
                    created_at=item.created_at.isoformat(),
                    updated_at=item.updated_at.isoformat()
                ) for item in items
            ],
            total=total,
            limit=limit,
            offset=offset
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/features/{feature_id}/backlog-items", response_model=BacklogItemResponse, status_code=201)
async def create_backlog_item(
    feature_id: int,
    item_data: BacklogItemCreate,
    service: BacklogService = Depends(get_backlog_service)
):
    """Create a new backlog item for a feature."""
    try:
        item = service.create_backlog_item(
            name=item_data.name,
            feature_id=feature_id,
            description=item_data.description,
            assignee=item_data.assignee
        )
        
        return BacklogItemResponse(
            id=item.id,
            name=item.name,
            description=item.description,
            status=item.status,
            assignee=item.assignee,
            feature_id=item.feature_id,
            ai_workflow_id=item.ai_workflow_id,
            created_at=item.created_at.isoformat(),
            updated_at=item.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/backlog-items/{item_id}", response_model=BacklogItemResponse)
async def get_backlog_item(
    item_id: int,
    service: BacklogService = Depends(get_backlog_service)
):
    """Get backlog item details."""
    try:
        item = service.get_backlog_item_by_id(item_id)
        
        if not item:
            raise HTTPException(status_code=404, detail="Backlog item not found")
        
        return BacklogItemResponse(
            id=item.id,
            name=item.name,
            description=item.description,
            status=item.status,
            assignee=item.assignee,
            feature_id=item.feature_id,
            ai_workflow_id=item.ai_workflow_id,
            created_at=item.created_at.isoformat(),
            updated_at=item.updated_at.isoformat()
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/backlog-items/{item_id}", response_model=BacklogItemResponse)
async def update_backlog_item(
    item_id: int,
    item_data: BacklogItemUpdate,
    service: BacklogService = Depends(get_backlog_service)
):
    """Update an existing backlog item."""
    try:
        item = service.update_backlog_item(
            item_id=item_id,
            name=item_data.name,
            description=item_data.description,
            status=item_data.status,
            assignee=item_data.assignee
        )
        
        if not item:
            raise HTTPException(status_code=404, detail="Backlog item not found")
        
        return BacklogItemResponse(
            id=item.id,
            name=item.name,
            description=item.description,
            status=item.status,
            assignee=item.assignee,
            feature_id=item.feature_id,
            ai_workflow_id=item.ai_workflow_id,
            created_at=item.created_at.isoformat(),
            updated_at=item.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/backlog-items/{item_id}", status_code=204)
async def delete_backlog_item(
    item_id: int,
    service: BacklogService = Depends(get_backlog_service)
):
    """Delete a backlog item."""
    try:
        success = service.delete_backlog_item(item_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Backlog item not found")
        
        return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/backlog-items/{item_id}/transition", response_model=BacklogItemResponse)
async def transition_backlog_item_status(
    item_id: int,
    transition: BacklogItemStatusTransition,
    service: BacklogService = Depends(get_backlog_service)
):
    """Transition backlog item status."""
    try:
        item = service.update_backlog_item(
            item_id=item_id,
            status=transition.status,
            assignee=transition.assignee
        )
        
        if not item:
            raise HTTPException(status_code=404, detail="Backlog item not found")
        
        return BacklogItemResponse(
            id=item.id,
            name=item.name,
            description=item.description,
            status=item.status,
            assignee=item.assignee,
            feature_id=item.feature_id,
            ai_workflow_id=item.ai_workflow_id,
            created_at=item.created_at.isoformat(),
            updated_at=item.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Task endpoints
@router.get("/backlog-items/{item_id}/tasks", response_model=TaskListResponse)
async def list_tasks(
    item_id: int,
    status: Optional[WorkItemStatus] = Query(None, description="Filter by task status"),
    service: TaskService = Depends(get_task_service)
):
    """List tasks for a backlog item with optional filtering."""
    try:
        tasks = service.list_tasks_by_backlog_item(
            backlog_item_id=item_id,
            status=status,
            ordered=True
        )
        
        return TaskListResponse(
            tasks=[
                TaskResponse(
                    id=task.id,
                    name=task.name,
                    description=task.description,
                    status=task.status,
                    assignee=task.assignee,
                    backlog_item_id=task.backlog_item_id,
                    processing_order=task.processing_order,
                    created_at=task.created_at.isoformat(),
                    updated_at=task.updated_at.isoformat()
                ) for task in tasks
            ]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/backlog-items/{item_id}/tasks", response_model=TaskResponse, status_code=201)
async def create_task(
    item_id: int,
    task_data: TaskCreate,
    service: TaskService = Depends(get_task_service)
):
    """Create a new task for a backlog item."""
    try:
        task = service.create_task(
            name=task_data.name,
            backlog_item_id=item_id,
            processing_order=task_data.processing_order,
            description=task_data.description,
            assignee=task_data.assignee
        )
        
        return TaskResponse(
            id=task.id,
            name=task.name,
            description=task.description,
            status=task.status,
            assignee=task.assignee,
            backlog_item_id=task.backlog_item_id,
            processing_order=task.processing_order,
            created_at=task.created_at.isoformat(),
            updated_at=task.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: int,
    service: TaskService = Depends(get_task_service)
):
    """Get task details."""
    try:
        task = service.get_task_by_id(task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return TaskResponse(
            id=task.id,
            name=task.name,
            description=task.description,
            status=task.status,
            assignee=task.assignee,
            backlog_item_id=task.backlog_item_id,
            processing_order=task.processing_order,
            created_at=task.created_at.isoformat(),
            updated_at=task.updated_at.isoformat()
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/tasks/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: int,
    task_data: TaskUpdate,
    service: TaskService = Depends(get_task_service)
):
    """Update an existing task."""
    try:
        task = service.update_task(
            task_id=task_id,
            name=task_data.name,
            description=task_data.description,
            status=task_data.status,
            assignee=task_data.assignee,
            processing_order=task_data.processing_order
        )
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return TaskResponse(
            id=task.id,
            name=task.name,
            description=task.description,
            status=task.status,
            assignee=task.assignee,
            backlog_item_id=task.backlog_item_id,
            processing_order=task.processing_order,
            created_at=task.created_at.isoformat(),
            updated_at=task.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/tasks/{task_id}", status_code=204)
async def delete_task(
    task_id: int,
    service: TaskService = Depends(get_task_service)
):
    """Delete a task."""
    try:
        success = service.delete_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/transition", response_model=TaskResponse)
async def transition_task_status(
    task_id: int,
    transition: TaskStatusTransition,
    service: TaskService = Depends(get_task_service)
):
    """Transition task status."""
    try:
        task = service.transition_status(
            task_id=task_id,
            new_status=transition.status,
            assignee=transition.assignee
        )
        
        return TaskResponse(
            id=task.id,
            name=task.name,
            description=task.description,
            status=task.status,
            assignee=task.assignee,
            backlog_item_id=task.backlog_item_id,
            processing_order=task.processing_order,
            created_at=task.created_at.isoformat(),
            updated_at=task.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/reorder", response_model=TaskResponse)
async def reorder_task(
    task_id: int,
    reorder_data: TaskReorder,
    service: TaskService = Depends(get_task_service)
):
    """Reorder a task by changing its processing order."""
    try:
        task = service.reorder_tasks(task_id, reorder_data.new_order)
        
        return TaskResponse(
            id=task.id,
            name=task.name,
            description=task.description,
            status=task.status,
            assignee=task.assignee,
            backlog_item_id=task.backlog_item_id,
            processing_order=task.processing_order,
            created_at=task.created_at.isoformat(),
            updated_at=task.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Export router for main app
__all__ = ["router"]
