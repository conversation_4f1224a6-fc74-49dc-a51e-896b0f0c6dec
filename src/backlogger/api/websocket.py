"""WebSocket handler for real-time updates."""

import json
import asyncio
from typing import Dict, List, Set
from datetime import datetime

from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..core.project_service import ProjectService
from ..core.backlog_service import BacklogService


# Connection manager for WebSocket connections
class ConnectionManager:
    """Manages WebSocket connections and broadcasting."""
    
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.connection_metadata: Dict[WebSocket, dict] = {}
    
    async def connect(self, websocket: WebSocket, room: str = "dashboard"):
        """Accept a WebSocket connection and add to room."""
        await websocket.accept()
        
        if room not in self.active_connections:
            self.active_connections[room] = set()
        
        self.active_connections[room].add(websocket)
        self.connection_metadata[websocket] = {
            "room": room,
            "connected_at": datetime.utcnow(),
            "last_ping": datetime.utcnow()
        }
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.connection_metadata:
            room = self.connection_metadata[websocket]["room"]
            if room in self.active_connections:
                self.active_connections[room].discard(websocket)
                if not self.active_connections[room]:
                    del self.active_connections[room]
            del self.connection_metadata[websocket]
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific WebSocket."""
        try:
            await websocket.send_text(message)
        except Exception:
            self.disconnect(websocket)
    
    async def broadcast_to_room(self, message: str, room: str = "dashboard"):
        """Broadcast a message to all connections in a room."""
        if room not in self.active_connections:
            return
        
        disconnected = set()
        for connection in self.active_connections[room].copy():
            try:
                await connection.send_text(message)
            except Exception:
                disconnected.add(connection)
        
        # Clean up disconnected connections
        for connection in disconnected:
            self.disconnect(connection)
    
    async def send_json_to_room(self, data: dict, room: str = "dashboard"):
        """Send JSON data to all connections in a room."""
        message = json.dumps(data)
        await self.broadcast_to_room(message, room)
    
    def get_room_stats(self, room: str = "dashboard") -> dict:
        """Get statistics for a room."""
        if room not in self.active_connections:
            return {"connections": 0, "room": room}
        
        connections = self.active_connections[room]
        return {
            "connections": len(connections),
            "room": room,
            "connected_since": [
                self.connection_metadata[conn]["connected_at"].isoformat()
                for conn in connections
                if conn in self.connection_metadata
            ]
        }


# Global connection manager
manager = ConnectionManager()


# Create router
router = APIRouter(tags=["websocket"])


# WebSocket message types
class MessageType:
    """WebSocket message types."""
    PROJECT_CREATED = "project_created"
    PROJECT_UPDATED = "project_updated"
    PROJECT_DELETED = "project_deleted"
    EPIC_CREATED = "epic_created"
    EPIC_UPDATED = "epic_updated"
    FEATURE_CREATED = "feature_created"
    FEATURE_UPDATED = "feature_updated"
    BACKLOG_ITEM_CREATED = "backlog_item_created"
    BACKLOG_ITEM_UPDATED = "backlog_item_updated"
    BACKLOG_ITEM_AI_READY = "backlog_item_ai_ready"
    BACKLOG_ITEM_AI_STARTED = "backlog_item_ai_started"
    BACKLOG_ITEM_AI_COMPLETED = "backlog_item_ai_completed"
    TASK_CREATED = "task_created"
    TASK_UPDATED = "task_updated"
    TASK_COMPLETED = "task_completed"
    AI_WORKFLOW_STATUS = "ai_workflow_status"
    DASHBOARD_UPDATE = "dashboard_update"
    PING = "ping"
    PONG = "pong"
    ERROR = "error"
    CONNECTION_STATUS = "connection_status"


async def create_message(message_type: str, data: dict, timestamp: str = None) -> dict:
    """Create a standardized WebSocket message."""
    return {
        "type": message_type,
        "data": data,
        "timestamp": timestamp or datetime.utcnow().isoformat()
    }


@router.websocket("/dashboard")
async def dashboard_websocket(websocket: WebSocket, db: Session = Depends(get_db)):
    """WebSocket endpoint for real-time dashboard updates."""
    await manager.connect(websocket, "dashboard")
    
    try:
        # Send initial connection status
        await manager.send_personal_message(
            json.dumps(await create_message(
                MessageType.CONNECTION_STATUS,
                {"status": "connected", "room": "dashboard"}
            )),
            websocket
        )
        
        # Send initial dashboard data
        project_service = ProjectService(db)
        backlog_service = BacklogService(db)
        
        # Get current stats
        projects, total_projects = project_service.list_projects(limit=100)
        ai_ready_items = backlog_service.list_items_ready_for_ai()
        ai_in_progress_items = backlog_service.list_items_in_ai_workflow()
        
        initial_data = {
            "total_projects": total_projects,
            "active_projects": len([p for p in projects if p.status.value == "active"]),
            "ai_queue_size": len(ai_ready_items),
            "ai_in_progress": len(ai_in_progress_items),
            "last_updated": datetime.utcnow().isoformat()
        }
        
        await manager.send_personal_message(
            json.dumps(await create_message(
                MessageType.DASHBOARD_UPDATE,
                initial_data
            )),
            websocket
        )
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for incoming message with timeout
                message = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                
                # Parse message
                try:
                    data = json.loads(message)
                    message_type = data.get("type")
                    
                    if message_type == MessageType.PING:
                        # Respond to ping with pong
                        await manager.send_personal_message(
                            json.dumps(await create_message(MessageType.PONG, {"status": "alive"})),
                            websocket
                        )
                        
                        # Update last ping time
                        if websocket in manager.connection_metadata:
                            manager.connection_metadata[websocket]["last_ping"] = datetime.utcnow()
                    
                    elif message_type == "subscribe":
                        # Handle subscription to specific events
                        subscription_types = data.get("data", {}).get("types", [])
                        # Store subscription preferences (simplified for now)
                        if websocket in manager.connection_metadata:
                            manager.connection_metadata[websocket]["subscriptions"] = subscription_types
                        
                        await manager.send_personal_message(
                            json.dumps(await create_message(
                                "subscription_confirmed",
                                {"types": subscription_types}
                            )),
                            websocket
                        )
                    
                    elif message_type == "get_stats":
                        # Send current room statistics
                        stats = manager.get_room_stats("dashboard")
                        await manager.send_personal_message(
                            json.dumps(await create_message("room_stats", stats)),
                            websocket
                        )
                
                except json.JSONDecodeError:
                    # Invalid JSON, send error
                    await manager.send_personal_message(
                        json.dumps(await create_message(
                            MessageType.ERROR,
                            {"message": "Invalid JSON format"}
                        )),
                        websocket
                    )
            
            except asyncio.TimeoutError:
                # Send ping to check if connection is still alive
                try:
                    await manager.send_personal_message(
                        json.dumps(await create_message(MessageType.PING, {"check": "alive"})),
                        websocket
                    )
                except Exception:
                    # Connection is dead
                    break
    
    except WebSocketDisconnect:
        pass
    except Exception as e:
        # Log error in real implementation
        print(f"WebSocket error: {e}")
    finally:
        manager.disconnect(websocket)


@router.websocket("/projects/{project_id}")
async def project_websocket(websocket: WebSocket, project_id: int, db: Session = Depends(get_db)):
    """WebSocket endpoint for project-specific updates."""
    room = f"project_{project_id}"
    await manager.connect(websocket, room)
    
    try:
        # Verify project exists
        project_service = ProjectService(db)
        project = project_service.get_project_by_id(project_id)
        
        if not project:
            await manager.send_personal_message(
                json.dumps(await create_message(
                    MessageType.ERROR,
                    {"message": f"Project {project_id} not found"}
                )),
                websocket
            )
            return
        
        # Send connection confirmation
        await manager.send_personal_message(
            json.dumps(await create_message(
                MessageType.CONNECTION_STATUS,
                {"status": "connected", "room": room, "project_id": project_id}
            )),
            websocket
        )
        
        # Send initial project data
        project_stats = project_service.get_project_stats(project_id)
        await manager.send_personal_message(
            json.dumps(await create_message(
                "project_stats",
                project_stats
            )),
            websocket
        )
        
        # Keep connection alive
        while True:
            try:
                message = await asyncio.wait_for(websocket.receive_text(), timeout=60.0)
                
                # Handle project-specific messages
                try:
                    data = json.loads(message)
                    message_type = data.get("type")
                    
                    if message_type == MessageType.PING:
                        await manager.send_personal_message(
                            json.dumps(await create_message(MessageType.PONG, {"status": "alive"})),
                            websocket
                        )
                    
                    elif message_type == "get_project_stats":
                        # Refresh project statistics
                        stats = project_service.get_project_stats(project_id)
                        await manager.send_personal_message(
                            json.dumps(await create_message("project_stats", stats)),
                            websocket
                        )
                
                except json.JSONDecodeError:
                    await manager.send_personal_message(
                        json.dumps(await create_message(
                            MessageType.ERROR,
                            {"message": "Invalid JSON format"}
                        )),
                        websocket
                    )
            
            except asyncio.TimeoutError:
                # Send ping
                try:
                    await manager.send_personal_message(
                        json.dumps(await create_message(MessageType.PING, {"check": "alive"})),
                        websocket
                    )
                except Exception:
                    break
    
    except WebSocketDisconnect:
        pass
    except Exception as e:
        print(f"Project WebSocket error: {e}")
    finally:
        manager.disconnect(websocket)


# Notification functions (called by other services)
async def notify_project_created(project_data: dict):
    """Notify all dashboard connections of a new project."""
    await manager.send_json_to_room(
        await create_message(MessageType.PROJECT_CREATED, project_data),
        "dashboard"
    )


async def notify_project_updated(project_data: dict):
    """Notify connections of project update."""
    # Notify dashboard
    await manager.send_json_to_room(
        await create_message(MessageType.PROJECT_UPDATED, project_data),
        "dashboard"
    )
    
    # Notify project-specific room
    project_room = f"project_{project_data['id']}"
    await manager.send_json_to_room(
        await create_message(MessageType.PROJECT_UPDATED, project_data),
        project_room
    )


async def notify_ai_workflow_status(item_data: dict):
    """Notify connections of AI workflow status changes."""
    message_type = MessageType.AI_WORKFLOW_STATUS
    
    if item_data.get("status") == "ready_for_ai":
        message_type = MessageType.BACKLOG_ITEM_AI_READY
    elif item_data.get("status") == "in_progress" and item_data.get("ai_workflow_id"):
        message_type = MessageType.BACKLOG_ITEM_AI_STARTED
    elif item_data.get("status") == "acceptance":
        message_type = MessageType.BACKLOG_ITEM_AI_COMPLETED
    
    # Notify dashboard
    await manager.send_json_to_room(
        await create_message(message_type, item_data),
        "dashboard"
    )
    
    # Notify project-specific room if we have feature info
    if "feature_id" in item_data:
        # Would need to look up project_id from feature_id in real implementation
        pass


async def notify_task_completed(task_data: dict):
    """Notify connections of task completion."""
    await manager.send_json_to_room(
        await create_message(MessageType.TASK_COMPLETED, task_data),
        "dashboard"
    )


# Health check endpoint
@router.get("/health")
async def websocket_health():
    """Get WebSocket service health and statistics."""
    total_connections = sum(len(connections) for connections in manager.active_connections.values())
    
    return {
        "status": "healthy",
        "total_connections": total_connections,
        "rooms": {
            room: len(connections)
            for room, connections in manager.active_connections.items()
        },
        "timestamp": datetime.utcnow().isoformat()
    }


# Export components
__all__ = ["router", "manager", "notify_project_created", "notify_project_updated", 
           "notify_ai_workflow_status", "notify_task_completed"]
