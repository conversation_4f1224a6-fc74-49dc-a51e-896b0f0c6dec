"""AI workflow trigger endpoints."""

from typing import List, Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..core.backlog_service import BacklogService
from ..models.backlog_item import BacklogItemStatus


# Pydantic models
class AIWorkflowTriggerResponse(BaseModel):
    id: int
    name: str
    status: BacklogItemStatus
    ai_workflow_id: Optional[str]
    message: str


class AIStatusResponse(BaseModel):
    id: int
    status: BacklogItemStatus
    ai_workflow_id: Optional[str]
    is_ai_processing: bool
    task_count: int
    completed_tasks: int
    updated_at: str


class AIWorkflowListResponse(BaseModel):
    ready_for_ai: List[dict]
    in_progress: List[dict]
    total_ready: int
    total_in_progress: int


class AIWorkflowCompleteRequest(BaseModel):
    success: bool = True
    error_message: Optional[str] = None


# Create router
router = APIRouter(tags=["ai-workflows"])


def get_backlog_service(db: Session = Depends(get_db)) -> BacklogService:
    """Dependency to get backlog service."""
    return BacklogService(db)


@router.post("/backlog-items/{item_id}/ready-for-ai", response_model=AIWorkflowTriggerResponse)
async def ready_for_ai(
    item_id: int,
    service: BacklogService = Depends(get_backlog_service)
):
    """Mark backlog item as ready for AI processing and trigger workflow."""
    try:
        item = service.ready_for_ai(item_id)
        
        return AIWorkflowTriggerResponse(
            id=item.id,
            name=item.name,
            status=item.status,
            ai_workflow_id=item.ai_workflow_id,
            message=f"Backlog item '{item.name}' marked as ready for AI processing"
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/backlog-items/{item_id}/ai-status", response_model=AIStatusResponse)
async def get_ai_status(
    item_id: int,
    service: BacklogService = Depends(get_backlog_service)
):
    """Get AI workflow status for a backlog item."""
    try:
        status_data = service.get_ai_status(item_id)
        
        if not status_data:
            raise HTTPException(status_code=404, detail="Backlog item not found")
        
        return AIStatusResponse(
            id=status_data["id"],
            status=status_data["status"],
            ai_workflow_id=status_data["ai_workflow_id"],
            is_ai_processing=status_data["is_ai_processing"],
            task_count=status_data["task_count"],
            completed_tasks=status_data["completed_tasks"],
            updated_at=status_data["updated_at"].isoformat()
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ai-workflows", response_model=AIWorkflowListResponse)
async def list_ai_workflows(
    service: BacklogService = Depends(get_backlog_service)
):
    """List all backlog items in AI workflow states."""
    try:
        ready_items = service.list_items_ready_for_ai()
        in_progress_items = service.list_items_in_ai_workflow()
        
        ready_data = [
            {
                "id": item.id,
                "name": item.name,
                "feature_id": item.feature_id,
                "task_count": len(item.tasks),
                "created_at": item.created_at.isoformat(),
                "updated_at": item.updated_at.isoformat()
            }
            for item in ready_items
        ]
        
        in_progress_data = [
            {
                "id": item.id,
                "name": item.name,
                "feature_id": item.feature_id,
                "ai_workflow_id": item.ai_workflow_id,
                "task_count": len(item.tasks),
                "completed_tasks": len([t for t in item.tasks if t.status.value == "done"]),
                "created_at": item.created_at.isoformat(),
                "updated_at": item.updated_at.isoformat()
            }
            for item in in_progress_items
        ]
        
        return AIWorkflowListResponse(
            ready_for_ai=ready_data,
            in_progress=in_progress_data,
            total_ready=len(ready_items),
            total_in_progress=len(in_progress_items)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/backlog-items/{item_id}/ai-workflow/complete", response_model=AIWorkflowTriggerResponse)
async def complete_ai_workflow(
    item_id: int,
    completion_data: AIWorkflowCompleteRequest,
    service: BacklogService = Depends(get_backlog_service)
):
    """Mark AI workflow as complete or failed."""
    try:
        if completion_data.success:
            item = service.complete_ai_workflow(item_id)
            message = f"AI workflow completed successfully for '{item.name}'"
        else:
            item = service.fail_ai_workflow(item_id, completion_data.error_message)
            message = f"AI workflow failed for '{item.name}'"
            if completion_data.error_message:
                message += f": {completion_data.error_message}"
        
        return AIWorkflowTriggerResponse(
            id=item.id,
            name=item.name,
            status=item.status,
            ai_workflow_id=item.ai_workflow_id,
            message=message
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/backlog-items/{item_id}/ai-workflow/start", response_model=AIWorkflowTriggerResponse)
async def start_ai_workflow(
    item_id: int,
    service: BacklogService = Depends(get_backlog_service)
):
    """Start AI workflow processing for a backlog item."""
    try:
        # Get the item
        item = service.get_backlog_item_by_id(item_id)
        if not item:
            raise HTTPException(status_code=404, detail="Backlog item not found")
        
        # Check if already ready for AI
        if item.status != BacklogItemStatus.READY_FOR_AI:
            raise ValueError(f"Backlog item must be in 'ready_for_ai' status, current status: {item.status}")
        
        # Move to in_progress status
        item = service.update_backlog_item(
            item_id=item_id,
            status=BacklogItemStatus.IN_PROGRESS,
            assignee="ai"
        )
        
        return AIWorkflowTriggerResponse(
            id=item.id,
            name=item.name,
            status=item.status,
            ai_workflow_id=item.ai_workflow_id,
            message=f"AI workflow started for '{item.name}'"
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ai-workflows/queue")
async def get_ai_workflow_queue(
    service: BacklogService = Depends(get_backlog_service)
):
    """Get AI workflow queue status."""
    try:
        ready_items = service.list_items_ready_for_ai()
        in_progress_items = service.list_items_in_ai_workflow()
        
        # Calculate queue metrics
        total_ready = len(ready_items)
        total_in_progress = len(in_progress_items)
        
        # Get oldest ready item
        oldest_ready = None
        if ready_items:
            oldest_ready = min(ready_items, key=lambda x: x.created_at)
        
        # Get workflow with most progress
        most_progress = None
        if in_progress_items:
            for item in in_progress_items:
                progress = len([t for t in item.tasks if t.status.value == "done"]) / len(item.tasks) if item.tasks else 0
                if most_progress is None or progress > most_progress.get("progress", 0):
                    most_progress = {
                        "id": item.id,
                        "name": item.name,
                        "progress": progress,
                        "completed_tasks": len([t for t in item.tasks if t.status.value == "done"]),
                        "total_tasks": len(item.tasks)
                    }
        
        return {
            "queue_status": {
                "total_ready": total_ready,
                "total_in_progress": total_in_progress,
                "capacity_available": total_in_progress < 5  # MAX_PARALLEL_WORKFLOWS
            },
            "oldest_ready": {
                "id": oldest_ready.id,
                "name": oldest_ready.name,
                "waiting_since": oldest_ready.created_at.isoformat()
            } if oldest_ready else None,
            "most_progress": most_progress,
            "timestamp": "2025-09-07T12:00:00Z"  # Use current time in real implementation
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ai-workflows/{workflow_id}/pause")
async def pause_ai_workflow(
    workflow_id: str,
    service: BacklogService = Depends(get_backlog_service)
):
    """Pause an active AI workflow."""
    try:
        # Find item by workflow ID
        in_progress_items = service.list_items_in_ai_workflow()
        item = next((i for i in in_progress_items if i.ai_workflow_id == workflow_id), None)
        
        if not item:
            raise HTTPException(status_code=404, detail="AI workflow not found")
        
        # Move back to ready_for_ai status
        item = service.update_backlog_item(
            item_id=item.id,
            status=BacklogItemStatus.READY_FOR_AI,
            assignee=None
        )
        
        return {
            "id": item.id,
            "name": item.name,
            "status": item.status,
            "ai_workflow_id": item.ai_workflow_id,
            "message": f"AI workflow paused for '{item.name}'"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ai-workflows/{workflow_id}/resume")
async def resume_ai_workflow(
    workflow_id: str,
    service: BacklogService = Depends(get_backlog_service)
):
    """Resume a paused AI workflow."""
    try:
        # Find item by workflow ID - check ready items
        ready_items = service.list_items_ready_for_ai()
        item = next((i for i in ready_items if i.ai_workflow_id == workflow_id), None)
        
        if not item:
            raise HTTPException(status_code=404, detail="Paused AI workflow not found")
        
        # Move to in_progress status
        item = service.update_backlog_item(
            item_id=item.id,
            status=BacklogItemStatus.IN_PROGRESS,
            assignee="ai"
        )
        
        return {
            "id": item.id,
            "name": item.name,
            "status": item.status,
            "ai_workflow_id": item.ai_workflow_id,
            "message": f"AI workflow resumed for '{item.name}'"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Export router for main app
__all__ = ["router"]
